/* Custom CSS for Financial Sketchers */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Animation classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
}

.animate-pulse-gentle {
    animation: pulse 2s ease-in-out infinite;
}

/* Initially hidden for animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Custom gradient backgrounds */
.gradient-primary {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, #eab308 0%, #ca8a04 100%);
}

.gradient-accent {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

/* Custom shadows */
.shadow-soft {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Card styles */
.card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Service card specific styles */
.service-card {
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #22c55e, #16a34a);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

/* Team card styles */
.team-card {
    position: relative;
    overflow: hidden;
}

.team-card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(22, 163, 74, 0.9));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 1rem;
}

.team-card:hover .overlay {
    opacity: 1;
}

/* Button styles */
.btn-primary {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #22c55e;
    padding: 0.75rem 2rem;
    border: 2px solid #22c55e;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-secondary:hover {
    background: #22c55e;
    color: white;
    transform: translateY(-2px);
}

/* Form styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    transition: border-color 0.3s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Navigation styles */
.nav-link.active {
    color: #22c55e;
    font-weight: 600;
}

/* Mobile menu animation */
.mobile-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.mobile-menu.active {
    max-height: 400px;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    /* Typography adjustments */
    h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
    }

    h2 {
        font-size: 2rem !important;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.5rem !important;
    }

    /* Card adjustments */
    .card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .service-card {
        margin-bottom: 1.5rem;
    }

    /* Hero section mobile optimization */
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Navigation mobile fixes */
    .mobile-menu {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    /* Form mobile optimization */
    .form-input,
    .form-textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Button mobile optimization */
    .btn-primary,
    .btn-secondary {
        padding: 1rem 2rem;
        font-size: 1rem;
        width: 100%;
        text-align: center;
    }

    /* Team cards mobile layout */
    .team-card {
        text-align: center;
    }

    /* Contact section mobile */
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Spacing adjustments */
    .py-20 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    .px-4 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    h1 {
        font-size: 2rem !important;
    }

    h2 {
        font-size: 1.75rem !important;
    }

    .card {
        padding: 1rem;
    }

    .py-20 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
        transform: none;
    }

    .hover-lift:active {
        transform: translateY(-2px);
    }

    .btn-primary:hover,
    .btn-secondary:hover {
        transform: none;
    }

    .btn-primary:active,
    .btn-secondary:active {
        transform: translateY(-1px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .btn-primary {
        border: 2px solid #000;
    }

    .btn-secondary {
        border: 3px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-on-scroll {
        opacity: 1;
        transform: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
    }

    /* This would require more extensive dark mode implementation */
}

/* Print styles */
@media print {
    .no-print,
    nav,
    .scroll-to-top,
    .mobile-menu-button {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    a {
        text-decoration: underline;
    }

    .bg-primary-600,
    .bg-secondary-600 {
        background: #fff !important;
        color: #000 !important;
        border: 2px solid #000;
    }
}
