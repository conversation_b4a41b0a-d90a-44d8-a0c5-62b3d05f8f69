# Brand Logos Instructions

## How to Add Actual Brand Logos

Currently, the brands section shows styled name cards. To replace them with actual logos:

### Step 1: Download Brand Logos
Download the following brand logos from the original Financial Sketchers website or official brand websites:

1. **bharti-axa.png** - Bharti AXA Life Insurance
2. **hdfc-ergo.png** - HDFC ERGO General Insurance  
3. **star-health.png** - Star Health Insurance
4. **max-bupa.png** - Max Bupa Health Insurance
5. **tata-aia.png** - Tata AIA Life Insurance
6. **bajaj-allianz.png** - Bajaj Allianz Insurance
7. **iifl.png** - IIFL Wealth Management
8. **mirae-asset.png** - Mirae Asset Mutual Funds
9. **uti-mutual-fund.png** - UTI Mutual Fund
10. **aditya-birla.png** - Aditya Birla Capital

### Step 2: Logo Specifications
- **Format**: PNG with transparent background preferred
- **Size**: Maximum 200px width, 80px height
- **Quality**: High resolution for crisp display
- **Background**: Transparent or white background

### Step 3: Replace HTML Code
In `index.html`, replace each brand name card with:

```html
<div class="brand-logo-item">
    <img src="images/bharti-axa.png" alt="Bharti AXA" class="brand-logo">
</div>
```

### Step 4: Update CSS (Optional)
If you want to keep the current card-style design but with logos, you can modify the CSS to show logos inside the cards instead of replacing the entire structure.

### Current Features:
✅ **Auto-scrolling animation** - Logos move from right to left continuously
✅ **Hover to pause** - Animation stops when hovering over any logo
✅ **Seamless loop** - Infinite scrolling with no gaps
✅ **Responsive design** - Adapts to different screen sizes
✅ **Smooth gradients** - Fade edges for professional look

### Alternative: Keep Current Design
The current card-based design is also professional and clearly shows all partner brands. You can keep this design if you prefer the clean, modern look over traditional logos.
